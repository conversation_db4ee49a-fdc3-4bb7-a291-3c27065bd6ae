defmodule AshJsonApiWrapper.CustomPagination.Test do
  use ExUnit.Case
  require Ash.Query
  @moduletag :custom_pagination

  defmodule TestingReq do
    def new do
      Req.new(
        retry: :transient,
        retry_delay: 2000,
        max_retries: 5,
        decode_body: true
      )
    end
  end

  # ── Custom paginator ──

  defmodule CustomPaginator do
    use AshJsonApiWrapper.Paginator

    defp cursor do
      case :ets.whereis(:cursor) do
        :undefined ->
          :ets.new(:cursor, [:set, :protected, :named_table])
          |> :ets.insert({self(), 1})

          1

        _ ->
          [{_, value} | _rest] = :ets.lookup(:cursor, self())
          value
      end
    end

    defp increment_cursor do
      :ets.insert(:cursor, {self(), cursor() + 1})
    end

    defp reset_cursor do
      cursor()
      :ets.insert(:cursor, {self(), 1})
    end

    def start(_opts) do
      reset_cursor()
      {:ok, %{params: %{"p" => 1}}}
    end

    def continue(_response, [], _) do
      :halt
    end

    def continue(_response, _entities, _opts) do
      increment_cursor()
      {:ok, %{params: %{"p" => cursor()}}}
    end
  end

  # ── Resource ──

  defmodule Users do
    use Ash.Resource,
      domain: AshJsonApiWrapper.CustomPagination.Test.Domain,
      data_layer: AshJsonApiWrapper.DataLayer,
      validate_domain_inclusion?: false

    json_api_wrapper do
      req(TestingReq)

      endpoints do
        base("https://65383945a543859d1bb1528e.mockapi.io/api/v1")

        endpoint :list_users do
          path("/users")
          limit_with {:param, "l"}
          runtime_sort? true
          paginator CustomPaginator
        end
      end
    end

    actions do
      read(:list_users) do
        primary?(true)

        pagination do
          offset?(true)
          required?(true)
          default_limit(50)
        end
      end
    end

    attributes do
      attribute :id, :integer do
        primary_key?(true)
        allow_nil?(false)
      end

      attribute(:name, :string)
    end
  end

  defmodule Domain do
    use Ash.Domain, validate_config_inclusion?: false

    resources do
      allow_unregistered?(true)
    end
  end

  # ── Test it! ──

  @tag :skip
  test "it works" do
    # Note: This test needs to be updated to use Req.Test instead of Tesla mocking
    # For now, we'll skip this test until Req testing is properly implemented
    :skip
  end
end
